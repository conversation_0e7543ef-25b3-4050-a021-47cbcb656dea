from odoo import models, fields, api
from datetime import datetime, timedelta
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class SportsGround(models.Model):
    _name = 'sports.ground'
    _description = "Sports Ground"
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string="Ground Name", required=True, tracking=True)
    gallery_image_ids = fields.One2many('sports.ground.gallery', 'ground_id', string="Gallery Images")
    main_360_image = fields.Binary(string="360° Main View Image")
    image_1920 = fields.Image(string='Image')
    complex_id = fields.Many2one('sports.complex', string="Complex")
    image = fields.Binary(string="Image", attachment=True)
    description = fields.Html(string="Description", sanitize=True)
    capacity = fields.Integer(string="Capacity", tracking=True)
    price_per_hour = fields.Float(string="Base Price per Hour", tracking=True)
    extra_player_charge = fields.Float(string="Extra Charge per Player",
                                       help="Additional charge for each player above the standard capacity")
    city = fields.Char(string="City", tracking=True)
    opening_timing = fields.Datetime(
        string="Opening DateTime",
        default=lambda self: fields.Datetime.now().replace(hour=8, minute=0, second=0, microsecond=0),
        help="Datetime when ground opens"
    )
    closing_timing = fields.Datetime(
        string="Closing DateTime",
        default=lambda self: fields.Datetime.now().replace(hour=22, minute=0, second=0, microsecond=0),
        help="Datetime when ground closes"
    )
    sports_type = fields.Selection([
        ('indoor', 'Indoor'),
        ('outdoor', 'Outdoor'),
    ], string="Type", default='outdoor', required=True)

    game_type = fields.Selection([
        ('team', 'Team'),
        ('individual', 'Individual'),
    ], string="Game Type", default='team', required=True)

    game_name = fields.Selection([
        ('cricket', 'Cricket'),
        ('football', 'Football'),
        ('basketball', 'Basketball'),
        ('tennis', 'Tennis'),
        ('volleyball', 'Volleyball'),
        ('badminton', 'Badminton'),
        ('hockey', 'Hockey'),
        ('swimming', 'Swimming'),
        ('boxing', 'Boxing'),
        ('gymnastics', 'Gymnastics'),
        ('athletics', 'Athletics'),
        ('table_tennis', 'Table Tennis'),
        ('snooker', 'Snooker'),
        ('chess', 'Chess'),
        ('karate', 'Karate'),
        ('yoga', 'Yoga'),
        ('dance', 'Dance')
    ], string="Game Name", default='cricket', required=True)
    slot_duration = fields.Selection([
        ('0.5', '30 Minutes'),
        ('1.0', '1 Hour'),
        ('1.5', '1.5 Hours'),
        ('2.0', '2 Hours'),
        ('3.0', '3 Hours'),
    ], string="Default Slot Duration", default='1.0', required=True)
    is_active = fields.Boolean(string="Is Active", default=True)
    active = fields.Boolean(string="Active", default=True)
    booking_ids = fields.One2many('sports.booking', 'ground_id', string="Bookings")
    slot_id = fields.One2many('sports.ground.slot', 'ground_id', string="Slots")
    color = fields.Integer(string="Color Index")

    min_players = fields.Integer(string="Minimum Players", default=1)
    max_players = fields.Integer(string="Maximum Players")

    def check_availability(self, date, start_time, end_time):
        """Check if the ground is available on a date for a given time range."""
        self.ensure_one()

        overlapping_bookings = self.env['sports.booking'].search_count([
            ('ground_id', '=', self.id),
            ('booking_date', '=', date),
            ('state', 'in', ['confirmed', 'in_progress']),
            '|',
            '&', ('start_time', '<=', start_time), ('end_time', '>', start_time),
            '&', ('start_time', '<', end_time), ('end_time', '>=', end_time)
        ])
        return overlapping_bookings == 0

    def generate_slots_for_date(self, date):
        """Generate time slots based on slot duration, opening and closing timings."""
        for ground in self:
            if not ground.slot_duration:
                continue

            slot_duration = float(ground.slot_duration)
            start_datetime = datetime.combine(date, ground.opening_timing.time())
            end_datetime = datetime.combine(date, ground.closing_timing.time())
            delta = timedelta(hours=slot_duration)

            while start_datetime + delta <= end_datetime:
                slot_exists = self.env['sports.ground.slot'].search_count([
                    ('ground_id', '=', ground.id),
                    ('date', '=', date),
                    ('start_time', '=', start_datetime.time().hour + start_datetime.time().minute / 60.0),
                ])
                if not slot_exists:
                    self.env['sports.ground.slot'].create({
                        'name': f"{start_datetime.strftime('%H:%M')} - {(start_datetime + delta).strftime('%H:%M')}",
                        'start_time': start_datetime.time().hour + start_datetime.time().minute / 60.0,
                        'end_time': (start_datetime + delta).time().hour + (
                                start_datetime + delta).time().minute / 60.0,
                        'ground_id': ground.id,
                        'date': date,
                        'price': ground.price_per_hour * slot_duration,
                    })

                start_datetime += delta


class SportsGroundGallery(models.Model):
    _name = 'sports.ground.gallery'
    _description = 'Sports Ground Gallery'

    name = fields.Char(string="Title")
    ground_id = fields.Many2one('sports.ground', string="Ground", required=True, ondelete='cascade')
    image = fields.Binary(string="Image", attachment=True)
