from odoo import models, fields


class SportsVenue(models.Model):
    _name = 'sports.venue'
    _description = 'Sports Venue'

    name = fields.Char(string='Venue Name', required=True)
    address = fields.Text(string='Venue Address')
    city = fields.Char(string='City')
    phone = fields.Char(string='Phone Number')
    email = fields.Char(string='Email')
    image = fields.Binary(string='Venue Image')
    capacity = fields.Integer(string='Capacity')
    active = fields.Boolean(default=True)

    game_selection = fields.Selection([
        ('cricket', 'Cricket'),
        ('football', 'Football'),
        ('basketball', 'Basketball'),
        ('tennis', 'Tennis'),
        ('volleyball', 'Volleyball'),
        ('badminton', 'Badminton'),
        ('hockey', 'Hockey'),
        ('swimming', 'Swimming'),
        ('boxing', 'Boxing'),
        ('gymnastics', 'Gymnastics'),
        ('athletics', 'Athletics'),
        ('table_tennis', 'Table Tennis'),
        ('snooker', 'Snooker'),
        ('chess', 'Chess'),
        ('karate', 'Karate'),
        ('yoga', 'Yoga'),
        ('dance', 'Dance'),
    ], string="Available Sports", required=True)
