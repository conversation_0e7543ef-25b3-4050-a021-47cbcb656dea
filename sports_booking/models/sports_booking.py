from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
from math import modf
import logging
import pytz

_logger = logging.getLogger(__name__)


class SportsBooking(models.Model):
    _name = 'sports.booking'
    _description = 'Sports Booking'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string="Booking Name", required=True, tracking=True)
    ground_id = fields.Many2one('sports.ground', string="Ground", required=True, tracking=True)
    players_count = fields.Integer(string="Players Count", default=1, required=True, tracking=True)
    start_datetime = fields.Datetime(
        string="Start Time",
        compute="_compute_start_and_stop_datetime",
        store=True,
        readonly=True,
        tracking=True
    )

    language_id = fields.Many2one('res.lang', string="Language",
                                  default=lambda self: self.env.user.lang and self.env['res.lang'].search(
                                      [('code', '=', self.env.user.lang)], limit=1),
                                  help="Language to use for communication and formatting."
                                  )

    stop_datetime = fields.Datetime(
        string="End Time",
        compute="_compute_start_and_stop_datetime",
        store=True,
        readonly=True,
        tracking=True
    )
    booking_id = fields.Many2one('res.users', string="Booked By", default=lambda self: self.env.user.id, tracking=True)
    user_id = fields.Many2one('res.users', string="Booked For", tracking=True)
    booking_date = fields.Date(string="Booking Date", tracking=True)
    player_ids = fields.Many2many('res.partner', string="Players", tracking=True)
    is_team_booking = fields.Boolean(string="Is Team Booking ?", default=False)
    team_id = fields.Many2one('sports.team', string="Team")
    game_name = fields.Selection([
        ('cricket', 'Cricket'),
        ('football', 'Football'),
        ('basketball', 'Basketball'),
        ('tennis', 'Tennis'),
        ('volleyball', 'Volleyball'),
        ('badminton', 'Badminton'),
        ('hockey', 'Hockey'),
        ('swimming', 'Swimming'),
        ('boxing', 'Boxing'),
        ('gymnastics', 'Gymnastics'),
        ('athletics', 'Athletics'),
        ('table_tennis', 'Table Tennis'),
        ('snooker', 'Snooker'),
        ('chess', 'Chess'),
        ('karate', 'Karate'),
        ('yoga', 'Yoga'),
        ('dance', 'Dance'),
    ], string="Game Name", tracking=True)

    slot_id = fields.Many2one(
        'sports.ground.slot',
        string="Slot",
        domain="[('is_booked', '=', False)]",
        tracking=True
    )

    total_price = fields.Float(string="Total Price", compute='_compute_slot_price', store=True, tracking=True)
    discount = fields.Float(string="Discount (%)", default=0.0, tracking=True)
    final_price = fields.Float(string="Final Price", compute='_compute_final_price', store=True, tracking=True)

    standard_players = fields.Integer(string="Standard Players", default=11, tracking=True)

    extra_player_charge = fields.Float(string="Extra Player Charge", compute='_compute_extra_charge', store=True)

    payment_method = fields.Selection([
        ('online', 'Online'),
        ('offline', 'Offline'),
    ], string="Payment Method", default='online', tracking=True)
    payment_tx_id = fields.Many2one('payment.transaction', string="Payment Transaction")
    payment_status = fields.Selection([
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
    ], string="Payment Status", required=True, default='pending')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('done', 'Done'),
        ('cancelled', 'Cancelled'),
    ], string="Status", default='draft', tracking=True)

    @api.constrains('payment_method', 'payment_status')
    def _check_payment_info(self):
        for rec in self:
            if rec.payment_method == 'online':
                if rec.payment_status == 'paid':
                    rec.state = 'confirmed'
                else:
                    raise ValidationError("For online payment, status must be 'Paid' to confirm the booking.")
            elif rec.payment_method == 'offline':
                if rec.payment_status != 'paid':
                    pass
                else:
                    rec.state = 'confirmed'

    duration_hours = fields.Float(string="Duration (Hours)", compute='_compute_duration_hours', store=True)

    price_per_hour = fields.Float(
        string="Sport Rate per Hour",
        compute="_compute_dynamic_sport_price",
        store=True
    )

    feedback_ids = fields.One2many('feedback.rating', 'booking_id', string="Feedback")
    feedback_count = fields.Integer(string="Feedback Count", compute='_compute_feedback_count')

    @api.depends('feedback_ids')
    def _compute_feedback_count(self):
        for rec in self:
            rec.feedback_count = len(rec.feedback_ids)

    @api.onchange('team_id')
    def _onchange_team_id(self):
        if self.team_id:
            self.players_count = len(self.team_id.member_ids)

    @api.onchange('is_team_booking')
    def _onchange_is_team(self):
        if not self.is_team_booking:
            self.team_id = False

    @api.depends('players_count', 'standard_players', 'ground_id.extra_player_charge')
    def _compute_extra_charge(self):
        for rec in self:
            rec.extra_player_charge = 0.0
            if not rec.ground_id:
                continue

            if rec.players_count > rec.standard_players:
                extra_players = rec.players_count - rec.standard_players
                rec.extra_player_charge = extra_players * rec.ground_id.extra_player_charge

    @api.depends('slot_id')
    def _compute_time_from_slots(self):
        for rec in self:
            if rec.slot_id:
                times = sorted(rec.slot_id, key=lambda s: s.start_time)
                rec.start_datetime = datetime.combine(rec.slot_id[0].date, datetime.min.time()) + timedelta(
                    hours=times[0].start_time)
                rec.stop_datetime = datetime.combine(rec.slot_id[0].date, datetime.min.time()) + timedelta(
                    hours=times[-1].end_time)
            else:
                rec.start_datetime = False
                rec.stop_datetime = False

    @api.depends('start_datetime', 'stop_datetime', 'price_per_hour', 'players_count', 'ground_id')
    def _compute_total_price(self):
        for rec in self:
            base_price = 0.0
            extra_player_cost = 0.0

            if rec.start_datetime and rec.stop_datetime:
                duration_hours = rec.stop_datetime - rec.start_datetime
                hours = duration_hours.total_seconds() / 3600.0
                base_price = hours * rec.ground_id.price_per_hour

            if rec.ground_id and rec.players_count > rec.ground_id.max_players:
                extra_players = rec.players_count - rec.ground_id.max_players
                extra_player_cost = extra_players * rec.ground_id.extra_player_charge

            rec.total_price = base_price + extra_player_cost
            rec.extra_player_charge = extra_player_cost

    @api.depends('ground_id', 'game_name')
    def _compute_dynamic_sport_price(self):
        for price in self:
            price.price_per_hour = 0.0
            if price.ground_id and price.game_name:
                price.price_per_hour = price.ground_id.price_per_hour if price.ground_id.price_per_hour else 0.0

    sport_rate = fields.Float(string="Sport Rate (per hour)", compute="_compute_sport_rate")

    @api.depends('ground_id', 'game_name')
    def _compute_sport_rate(self):
        for rec in self:
            rec.sport_rate = 0.0
            if rec.ground_id and rec.game_name:
                rec.sport_rate = rec.ground_id.price_per_hour

    @api.depends('start_datetime', 'stop_datetime')
    def _compute_duration_hours(self):
        for rec in self:
            if rec.start_datetime and rec.stop_datetime:
                duration_hours = rec.stop_datetime - rec.start_datetime
                rec.duration_hours = duration_hours.total_seconds() / 3600.0
            else:
                rec.duration_hours = 0.0

    @api.depends('total_price', 'discount')
    def _compute_final_price(self):
        for rec in self:
            rec.final_price = rec.total_price - (rec.total_price * (rec.discount / 100.0))

    @api.onchange('booking_date', 'ground_id')
    def _onchange_booking_slots(self):
        if self.booking_date and self.ground_id:
            slots = self.env['sports.ground.slot'].search([
                ('ground_id', '=', self.ground_id.id),
                ('date', '=', self.booking_date),
                ('is_booked', '=', False),
            ])
            print(slots)
            return {
                'domain': {
                    'slot_id': [('id', 'in', slots.ids)]
                }
            }
        else:
            return {
                'domain': {
                    'slot_id': [('id', '=', False)]
                }
            }

    @api.depends('slot_id')
    def _compute_start_and_stop_datetime(self):
        for rec in self:
            if rec.slot_id:
                slot = rec.slot_id
                user_tz = self.env.user.tz or 'UTC'
                tz = pytz.timezone(user_tz)

                def float_to_localized_datetime(date, float_time):
                    frac, hour = modf(float_time)
                    minutes = int(round(frac * 60))
                    local_dt = datetime.combine(date, datetime.min.time()) + timedelta(hours=int(hour), minutes=minutes)
                    localized = tz.localize(local_dt, is_dst=None)
                    return localized.astimezone(pytz.utc).replace(tzinfo=None)

                rec.start_datetime = float_to_localized_datetime(slot.date, slot.start_time)
                rec.stop_datetime = float_to_localized_datetime(slot.date, slot.end_time)
            else:
                rec.start_datetime = False
                rec.stop_datetime = False

    @api.constrains('start_datetime', 'stop_datetime')
    def _check_booking_time(self):
        for rec in self:
            if rec.start_datetime >= rec.stop_datetime:
                raise ValidationError(_("End time must be after start time."))

    def action_send_mail(self):
        for rec in self:
            template = self.env.ref('sports_booking.email_template_booking_confirmation')
            if template and rec.booking_id.email:
                template.send_mail(rec.id, force_send=True)

    def action_confirm(self):
        for rec in self:
            if not rec.slot_id:
                raise ValidationError("Please select at least one slot.")

            for slot in rec.slot_id:
                if slot.is_booked:
                    raise ValidationError(f"The slot '{slot.name}' is already booked.")
                slot.is_booked = True

            if rec.payment_method == 'Online' and rec.payment_status != 'paid':
                raise UserError("For online payment, status must be 'Paid' to confirm the booking.")
            rec.state = 'confirmed'

    def action_cancel(self):
        for rec in self:
            for slot in rec.slot_id:
                slot.is_booked = False
            rec.state = 'cancelled'

    def action_done(self):
        self.write({'state': 'done'})
        self.slot_id.write({'is_booked': False})

    def action_set_draft(self):
        for rec in self:
            rec.state = 'draft'
