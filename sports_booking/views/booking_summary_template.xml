<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="booking_summary_template" name="Booking Summary">
        <t t-call="website.layout">
            <div class="container mt-5">
                <h2 class="mb-4">Booking Summary</h2>

                <div class="card shadow-sm p-4">
                    <h4>Ground:
                        <t t-esc="ground.name"/>
                    </h4>
                    <p>City:
                        <t t-esc="ground.city"/>
                    </p>
                    <p>Date:
                        <t t-esc="slot.date"/>
                    </p>
                    <p>Time:
                        <t t-esc="slot.start_time"/>
                        -
                        <t t-esc="slot.end_time"/>
                    </p>
                    <p>Price: ₹
                        <t t-esc="slot.price"/>
                    </p>
                </div>

                <form method="POST" action="/book/confirm" class="mt-4">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                    <input type="hidden" name="slot_id" t-att-value="slot.id"/>

                    <div class="mb-3">
                        <label>Select Payment Method:</label>
                        <select name="payment_method" class="form-control" required="required">
                            <option value="offline">Offline (Pay at Ground)</option>
                            <option value="online">Online Payment</option>
                        </select>
                    </div>

                    <div class="alert alert-info" id="offline-payment-info" style="display: none;">
                        For offline payment, your booking will be confirmed immediately. Please pay at the ground when
                        you arrive.
                    </div>

                    <button type="submit" class="btn btn-success">
                        Confirm Booking
                    </button>
                </form>
            </div>

            <script>
                document.querySelector('select[name="payment_method"]').addEventListener('change', function() {
                var infoDiv = document.getElementById('offline-payment-info');
                if (this.value === 'offline') {
                infoDiv.style.display = 'block';
                } else {
                infoDiv.style.display = 'none';
                }
                });
            </script>
        </t>
    </template>

    <!-- BOOKING CONFIRMATION -->
    <template id="booking_confirmation_template" name="Booking Confirmation Page">
        <t t-call="website.layout">
            <div class="container mt-5 mb-5 text-center">
                <h1 class="text-success">Booking Confirmed!</h1>
                <p class="lead mt-3">
                    Thank you
                    <strong t-esc="booking_name"/>
                    for your booking.
                </p>
                <div t-if="payment_method == 'offline'" class="alert alert-warning">
                    <h4>Important!</h4>
                    <p>Please remember to bring your payment when you arrive at the ground.</p>
                    <p>Your booking reference:
                        <strong t-esc="booking_ref"/>
                    </p>
                </div>
                <a href="/" class="btn btn-primary mt-4">Back to Home</a>
            </div>
        </t>
    </template>
</odoo>
