<?xml version="1.0" encoding="UTF-8" ?>
<odoo>  
    <template id="homepage_template" name="Sports Homepage">
        <t t-call="website.layout">
            <div class="container mt-5">
                <h2>Pick a Sport</h2>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label>Select Date</label>
                        <input type="date" id="booking_date" class="form-control"/>
                    </div>
                    <div class="col-md-4">
                        <label>Select Venue</label>
                        <select id="venue_select" class="form-control">
                            <t t-foreach="request.env['sports.ground'].sudo().search([])" t-as="venue">
                                <option t-att-value="venue.id">
                                    <t t-esc="venue.name"/>
                                </option>
                            </t>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label>Select Sport</label>
                        <select id="sport_select" class="form-control">
                            <option>Basketball</option>
                            <option>Cricket</option>
                            <option>Pickleball</option>
                            <option>Volleyball</option>
                        </select>
                    </div>
                    <div class="col-md-12 mt-3 text-center">
                        <button class="btn btn-danger" id="check_slot_btn">Check Slot</button>
                    </div>
                </div>
                <div id="slot_results"></div>
                <script type="text/javascript">
                    $(document).ready(function () {
                    $('#check_slot_btn').click(function () {
                    const bookingDate = $('#booking_date').val();
                    const venueId = $('#venue_select').val();
                    const sport = $('#sport_select').val();

                    if (!bookingDate || !venueId || !sport) {
                    alert('Please select all fields.');
                    return;
                    }

                    // Call your backend (e.g. via /search_results or custom route)
                    $.ajax({
                    url: "/search_results",
                    type: "GET",
                    data: {
                    date: bookingDate,
                    city: venueId // or modify this if needed to use a correct filter
                    },
                    success: function (result) {
                    $('#slot_results').html($(result).find('#slot_results').html());
                    },
                    error: function () {
                    alert("Failed to fetch slots. Please try again.");
                    }
                    });
                    });
                    });
                </script>
            </div>
        </t>
    </template>
</odoo>
