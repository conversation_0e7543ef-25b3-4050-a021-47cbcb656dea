from odoo import http
from odoo.http import request


class SportsWebsite(http.Controller):

    @http.route('/', type='http', auth='public', website=True)
    def homepage(self, **kwargs):
        grounds = request.env['sports.ground'].sudo().search([], limit=6)
        return request.render('sports_booking.homepage_template', {'grounds': grounds})

    @http.route('/search_results', type='http', auth="public", website=True)
    def search_results(self, city=None, date=None, **kwargs):
        domain = []
        if city:
            domain.append(('city', 'ilike', city))
        if date:
            domain.append(('slot_id.date', '=', date))

        grounds = request.env['sports.ground'].sudo().search(domain)
        return request.render('sports_booking.homepage_template', {
            'grounds': grounds,
            'search_city': city,
            'search_date': date
        })

    @http.route('/book/<int:ground_id>', type='http', auth='public', website=True)
    def book_ground(self, ground_id, **kwargs):
        ground = request.env['sports.ground'].sudo().browse(ground_id)
        slots = request.env['sports.ground.slot'].sudo().search([('ground_id', '=', ground_id)])
        return request.render('sports_booking.slot_booking_template', {
            'ground': ground,
            'slots': slots,
        })

    @http.route('/book/slot/<int:slot_id>', type='http', auth='public', website=True)
    def booking_summary(self, slot_id, **kwargs):
        slot = request.env['sports.ground.slot'].sudo().browse(slot_id)
        if not slot.exists():
            return request.not_found()
        return request.render('sports_booking.booking_summary_template', {
            'slot': slot,
            'ground': slot.ground_id,
        })

    @http.route('/book/confirm', type='http', auth='public', website=True, methods=['POST'])
    def confirm_booking(self, **post):
        # try:
        slot_id = int(post.get('slot_id'))
        payment_method = post.get('payment_method')

        slot = request.env['sports.ground.slot'].sudo().browse(slot_id)

        booking = request.env['sports.booking'].sudo().create({
            'name': f"Booking for {slot.ground_id.name}",
            'ground_id': slot.ground_id.id,
            'slot_id': slot.id,
            'booking_date': slot.date,
            'user_id': request.env.user.id,
            'payment_method': payment_method,
            'state': 'draft',
            'game_name': slot.ground_id.game_name,
        })

        if payment_method == 'Online':
            return request.redirect('/payment/process?reference=%s&amount=%s&currency_id=%s&booking_id=%s' % (
                booking.name,
                100.0,
                request.env.ref('base.INR').id,
                booking.id
            ))

        booking.action_confirm()
        return request.redirect('/book/confirmed/%s' % booking.name)

    @http.route('/payment/success', type='http', auth='public', website=True)
    def payment_success(self, **post):
        booking_name = post.get('reference')
        booking = request.env['sports.booking'].sudo().search([('name', '=', booking_name)], limit=1)

        if booking:
            booking.state = 'confirmed'
        return request.render('sports_booking.booking_confirmation_template', {})

    @http.route('/book/thank-you', type='http', auth='public', website=True)
    def booking_thank_you(self):
        return request.render('sports_booking.booking_confirmation_template', {})
