.o_executive_dashboard {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: calc(100vh - 40px);
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    height: auto;
    max-height: none;
}

.o_executive_dashboard .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    transition: box-shadow 0.15s ease-in-out;
}

.o_executive_dashboard .card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.o_executive_dashboard .card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.o_executive_dashboard .card-body {
    padding: 1.25rem;
}

.o_executive_dashboard .chart-container {
    position: relative;
    height: 400px;
    width: 100%;
    overflow: hidden;
}

.o_executive_dashboard .chart-container canvas {
    max-width: 100%;
    height: 100%;
    display: block;
}

.o_executive_dashboard .spinner-border {
    width: 3rem;
    height: 3rem;
    margin: 2rem auto;
}

.o_executive_dashboard .bg-primary {
    background-color: #007bff !important;
}

.o_executive_dashboard .bg-success {
    background-color: #28a745 !important;
}

.o_executive_dashboard .bg-info {
    background-color: #17a2b8 !important;
}

.o_executive_dashboard .bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.o_executive_dashboard .opacity-75 {
    opacity: 0.75;
}

.o_executive_dashboard * {
    box-sizing: border-box;
}

.o_executive_dashboard .container-fluid {
    max-width: 100%;
    padding: 0 15px;
}

.o_executive_dashboard .row {
    margin: 0 -15px;
}

.o_executive_dashboard .col-md-3,
.o_executive_dashboard .col-sm-6,
.o_executive_dashboard .col-lg-6,
.o_executive_dashboard .col-md-12,
.o_executive_dashboard .col-12 {
    padding: 0 15px;
}

.o_executive_dashboard .dashboard-content {
    animation: fadeIn 0.5s ease-in-out;
    width: 100%;
    max-width: 100%;
}

.o_action_manager .o_executive_dashboard {
    height: auto !important;
    max-height: none !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;
}

.o_action_manager {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: auto !important;
    max-height: none !important;
}

.o_action_manager > .o_executive_dashboard {
    position: relative !important;
    height: auto !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: none !important;
}

.o_web_client .o_action_manager .o_executive_dashboard {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: auto !important;
    min-height: calc(100vh - 40px) !important;
    max-height: none !important;
    position: relative !important;
}

.o_content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: auto !important;
    max-height: none !important;
}

.o_action_manager .o_action {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: auto !important;
    max-height: none !important;
}

.o_action_manager .o_action .o_executive_dashboard {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: auto !important;
    min-height: calc(100vh - 40px) !important;
    max-height: none !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.o_executive_dashboard .alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
}

.o_executive_dashboard h2 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 2rem;
}

.o_executive_dashboard .card-title {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.o_executive_dashboard .card h4 {
    font-weight: 700;
    font-size: 1.75rem;
}

.o_executive_dashboard .card h6 {
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@media (max-width: 768px) {
    .o_executive_dashboard {
        padding: 10px;
    }

    .o_executive_dashboard .chart-container {
        height: 300px;
    }
}
