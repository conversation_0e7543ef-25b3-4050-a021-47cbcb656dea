{
    'name': 'Sports Ground Booking',
    'version': '18.0',
    'summary': 'Book online sports grounds',
    'description': 'This module allows users to book sports grounds online.',
    'category': 'Website',
    'license': 'LGPL-3',
    'website': '',
    'author': '',
    'depends': ['base', 'website', 'portal', 'auth_signup', 'mail'],
    'data': [
        'data/sports_booking_email_template.xml',
        'security/ir.model.access.csv',
        'security/security.xml',
        'wizard/report_booking_pdf.xml',
        'wizard/revenue_report_template.xml',
        'views/homepage_template.xml',
        'views/executive_dashboard_template.xml',
        # 'views/assets.xml',
        'views/slot_booking_template.xml',
        'views/booking_summary_template.xml',
        'views/sports_booking_menu.xml',
        'views/sports_venue.xml',
        'views/feedback_rating_views.xml',  # contains action_feedback_ratings
        'views/sports_booking.xml',
        #  load these first
        'views/executive_dashboard_menu.xml',
        # then load this (which uses %(action_feedback_ratings)d)

        'views/sports_complex.xml',
        'views/sports_ground.xml',
        'views/sports_equipment.xml',
        'views/sports_slot.xml',
        'views/sports_coach.xml',
        'views/sports_membership.xml',
        'views/sports_equipment_rental.xml',
        'views/sports_team_views.xml',
        # 'views/whatsapp_integration.xml',
        'wizard/booking_report_wizard_view.xml',
        'wizard/revenue_report_wizard_view.xml',
    ],
     'assets': {
        'web.assets_backend': [
            'sports_booking/static/src/js/executive_dashboard.js',
            'sports_booking/static/src/css/executive_dashboard.css',
            'sports_booking/static/src/xml/executive_dashboard.xml',
        ],
    },

    'installable': True,
    'application': True,
}
